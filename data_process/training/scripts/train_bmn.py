#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BMN训练启动脚本
用于多类别时间动作定位任务
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

def check_environment():
    """检查训练环境"""
    print("🔍 检查训练环境...")
    
    # 检查CUDA
    try:
        import torch
        if torch.cuda.is_available():
            print(f"✅ CUDA可用: {torch.cuda.get_device_name(0)}")
            print(f"   GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB")
        else:
            print("⚠️  CUDA不可用，将使用CPU训练（速度较慢）")
    except ImportError:
        print("❌ PyTorch未安装")
        return False
    
    # 检查MMAction2
    try:
        import mmaction
        print(f"✅ MMAction2已安装: {mmaction.__version__}")
    except ImportError:
        print("❌ MMAction2未安装")
        return False
    
    return True

def check_data_files():
    """检查数据文件"""
    print("\n📁 检查数据文件...")
    
    # 检查配置文件
    config_file = "../configs/bmn_multiclass_tad_config.py"
    if not os.path.exists(config_file):
        print(f"❌ 配置文件不存在: {config_file}")
        return False
    print(f"✅ 配置文件: {config_file}")
    
    # 检查标注文件
    ann_files = [
        "../../../data/MultiClassTAD/multiclass_tad_train_reconstructed.json",
        "../../../data/MultiClassTAD/multiclass_tad_val_reconstructed.json"
    ]
    
    for ann_file in ann_files:
        if not os.path.exists(ann_file):
            print(f"❌ 标注文件不存在: {ann_file}")
            return False
        print(f"✅ 标注文件: {ann_file}")
    
    # 检查特征文件目录
    feature_dir = "../../../data/MultiClassTAD/features_slowonly_reconstructed"
    if not os.path.exists(feature_dir):
        print(f"❌ 特征目录不存在: {feature_dir}")
        return False
    
    # 统计特征文件数量
    csv_files = list(Path(feature_dir).rglob("*.csv"))
    print(f"✅ 特征目录: {feature_dir} ({len(csv_files)} 个CSV文件)")
    
    return True

def create_work_dir():
    """创建工作目录"""
    work_dir = "../../../work_dirs/bmn_multiclass_tad_slowonly"
    os.makedirs(work_dir, exist_ok=True)
    print(f"✅ 工作目录: {work_dir}")
    return work_dir

def start_training(config_file, resume=False, gpu_ids="0"):
    """启动训练"""
    print(f"\n🚀 开始BMN训练...")
    print(f"   配置文件: {config_file}")
    print(f"   GPU设备: {gpu_ids}")

    # 设置CUDA设备环境变量
    os.environ['CUDA_VISIBLE_DEVICES'] = gpu_ids

    # 构建训练命令
    cmd = [
        "python", "../../../mmaction2/tools/train.py",
        config_file
    ]

    if resume:
        cmd.extend(["--resume"])
    
    print(f"   执行命令: {' '.join(cmd)}")
    print("=" * 60)
    
    # 执行训练
    try:
        subprocess.run(cmd, check=True)
        print("\n🎉 训练完成!")
    except subprocess.CalledProcessError as e:
        print(f"\n❌ 训练失败: {e}")
        return False
    except KeyboardInterrupt:
        print(f"\n⏹️  训练被用户中断")
        return False
    
    return True

def main():
    parser = argparse.ArgumentParser(description='BMN训练启动脚本')
    parser.add_argument('--config', default='bmn_multiclass_tad_config.py',
                       help='配置文件路径')
    parser.add_argument('--resume', action='store_true',
                       help='从检查点恢复训练')
    parser.add_argument('--gpu-ids', default='0',
                       help='GPU设备ID，多个用逗号分隔')
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("🎯 BMN多类别时间动作定位训练")
    print("=" * 60)
    
    # 检查环境
    if not check_environment():
        print("❌ 环境检查失败，请先安装必要的依赖")
        return
    
    # 检查数据文件
    if not check_data_files():
        print("❌ 数据文件检查失败，请确保数据准备完整")
        return
    
    # 创建工作目录
    create_work_dir()
    
    # 开始训练
    success = start_training(args.config, args.resume, args.gpu_ids)
    
    if success:
        print("\n📋 训练完成后，你可以:")
        print("   1. 查看训练日志和指标")
        print("   2. 使用最佳模型进行测试")
        print("   3. 可视化训练结果")
    else:
        print("\n💡 如果遇到问题，请检查:")
        print("   1. GPU内存是否足够（可以减小batch_size）")
        print("   2. 数据路径是否正确")
        print("   3. 依赖包是否完整安装")

if __name__ == '__main__':
    main()
